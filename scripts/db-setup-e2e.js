#!/usr/bin/env node

/**
 * E2E Database Setup Script
 * Sets up the database for E2E testing with proper schema and seed data
 */

import { Pool } from 'pg'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Database configuration for E2E tests
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '********************************************************************/benefitlens',
  ssl: false
})

async function setupE2EDatabase() {
  console.log('🗄️ Setting up E2E database...')
  
  try {
    // Test connection
    const client = await pool.connect()
    console.log('✅ Database connection established')
    
    // Run schema setup
    console.log('📋 Running database schema...')
    const schemaPath = path.join(process.cwd(), 'database', 'schema.sql')
    if (fs.existsSync(schemaPath)) {
      const schema = fs.readFileSync(schemaPath, 'utf8')
      await client.query(schema)
      console.log('✅ Database schema applied')
    } else {
      console.warn('⚠️ Schema file not found, skipping schema setup')
    }
    
    // Run seed data
    console.log('🌱 Loading seed data...')
    const seedPath = path.join(process.cwd(), 'database', 'seed.sql')
    if (fs.existsSync(seedPath)) {
      const seed = fs.readFileSync(seedPath, 'utf8')
      await client.query(seed)
      console.log('✅ Seed data loaded')
    } else {
      console.warn('⚠️ Seed file not found, skipping seed data')
    }
    
    // Add E2E specific test data
    console.log('🧪 Adding E2E test data...')
    await addE2ETestData(client)
    
    client.release()
    console.log('✅ E2E database setup complete')
    
  } catch (error) {
    console.error('❌ E2E database setup failed:', error)
    throw error
  } finally {
    await pool.end()
  }
}

async function addE2ETestData(client) {
  // Clean up any existing E2E test data
  await client.query(`
    DELETE FROM magic_link_tokens WHERE email LIKE '%@%.e2e';
    DELETE FROM user_benefit_rankings WHERE user_id IN (
      SELECT id FROM users WHERE email LIKE '%@%.e2e'
    );
    DELETE FROM users WHERE email LIKE '%@%.e2e';
  `)
  
  // Add E2E test users
  const testUsers = [
    { email: 'user1@techcorp.e2e', firstName: 'Test', lastName: 'User1', role: 'user' },
    { email: 'user2@industries.e2e', firstName: 'Test', lastName: 'User2', role: 'user', isPremium: true },
    { email: '<EMAIL>', firstName: 'Test', lastName: 'Admin', role: 'admin' },
    { email: '<EMAIL>', firstName: 'Test', lastName: 'Admin2', role: 'admin' },
    { email: '<EMAIL>', firstName: 'Test', lastName: 'Admin3', role: 'admin' },
    { email: '<EMAIL>', firstName: 'Test', lastName: 'Admin4', role: 'admin' },
    { email: '<EMAIL>', firstName: 'Super', lastName: 'Admin', role: 'super_admin' },
    { email: 'simple-user1@simpletest.e2e', firstName: 'Simple', lastName: 'User1', role: 'user' },
    { email: 'simple-user2@simpletest.e2e', firstName: 'Simple', lastName: 'User2', role: 'user' }
  ]
  
  for (const user of testUsers) {
    await client.query(`
      INSERT INTO users (email, first_name, last_name, role, is_premium, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      ON CONFLICT (email) DO UPDATE SET
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        role = EXCLUDED.role,
        is_premium = EXCLUDED.is_premium,
        updated_at = NOW()
    `, [user.email, user.firstName, user.lastName, user.role, user.isPremium || false])
  }
  
  console.log('✅ E2E test users created')
  
  // Ensure we have test companies and benefits for dispute testing
  const testCompanyResult = await client.query(`
    INSERT INTO companies (name, website, description, created_at, updated_at)
    VALUES ('E2E Test Company', 'https://e2etest.com', 'Test company for E2E testing', NOW(), NOW())
    ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
    RETURNING id
  `)
  
  const companyId = testCompanyResult.rows[0].id
  
  // Add a test benefit for dispute testing
  const benefitResult = await client.query(`
    SELECT id FROM benefits WHERE name = 'Health Insurance' LIMIT 1
  `)
  
  if (benefitResult.rows.length > 0) {
    const benefitId = benefitResult.rows[0].id
    
    await client.query(`
      INSERT INTO company_benefits (company_id, benefit_id, verified, created_at, updated_at)
      VALUES ($1, $2, true, NOW(), NOW())
      ON CONFLICT (company_id, benefit_id) DO UPDATE SET updated_at = NOW()
    `, [companyId, benefitId])
    
    console.log('✅ E2E test company benefits created')
  }
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupE2EDatabase()
    .then(() => {
      console.log('🎉 E2E database setup completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 E2E database setup failed:', error)
      process.exit(1)
    })
}

export { setupE2EDatabase }
